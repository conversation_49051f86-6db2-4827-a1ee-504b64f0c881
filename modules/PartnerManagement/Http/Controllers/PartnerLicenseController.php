<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Exception;
use Modules\PartnerManagement\Http\Requests\QLMNUpdateRequest;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Transformers\PartnerLicenseTransformer;

class PartnerLicenseController extends Controller
{
    /**
     * Hiển thị danh sách license
     */
    public function index(Request $request)
    {
        $query = PartnerLicense::forAuthCompany()->with('pms_unit')
            ->select('id', 'code', 'month', 'type', 'project_code', 'project_account', 'activated_at', 'expired_at', 'assigned_at', 'status', 'project_unit_ref');

        if ($request->filled('product_id')) {
            $query->where('project_code', $request->input('product_id'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('project_account')) {
            $query->where('project_account', 'like', '%' . $request->input('project_account') . '%');
        }

        if ($request->filled('expired_in')) {
            $days = (int) $request->input('expired_in');
            $query->whereDate('expired_at', '<=', now()->addDays($days));
        }

        if ($request->filled('month')) {
            $query->where('month', $request->input('month'));
        }

        if ($request->filled('province') && !$request->filled('district')) {
            $sgd = PMSUnit::where('id', $request->input('province'))->soGiaoDuc()->isActive()->first();
            $units = $sgd->getAllSchools()->pluck('id')->toArray();
            $query->whereIn('project_unit_ref', $units);
        }

        if ($request->filled('district')) {
            $pgd = PMSUnit::where('id', $request->input('district'))->phongGiaoDuc()->isActive()->first();
            $units = $pgd->getDepartmentSchools()->pluck('id')->toArray();
            $query->whereIn('project_unit_ref', $units);
        }

        $licenses = $query->orderBy('updated_at', 'DESC')->paginate(20);
        //dd($licenses->toArray());

        return api_response(true, 'Success', $licenses, 200, null, PartnerLicenseTransformer::class, true);
    }

    /**
     * Kích hoạt license
     */
    public function activate(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $license = PartnerLicense::forAuthCompany()->findOrFail($id);
            $license->status = PartnerLicense::STATUS_ACTIVATED;
            $license->save();
            DB::commit();
            return api_response(true, 'Kích hoạt giấy phép thành công!', $license);
        } catch (Exception $e) {
            DB::rollBack();
            return api_response(false, $e->getMessage(), null, 422);
        }
    }

    /**
     * Khóa license (tạm khóa)
     */
    public function lock(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $license = PartnerLicense::forAuthCompany()
                ->where('status', PartnerLicense::STATUS_ACTIVATED)
                ->findOrFail($id);

            // Kiểm tra xem license có còn hạn không
            if ($license->expired_at && $license->expired_at->isPast()) {
                return api_response(false, 'Không thể khóa giấy phép đã hết hạn!', null, 422);
            }

            $license->status = PartnerLicense::STATUS_PENDING;
            $license->save();
            DB::commit();
            return api_response(true, 'Khóa giấy phép thành công!', $license);
        } catch (Exception $e) {
            DB::rollBack();
            return api_response(false, $e->getMessage(), null, 422);
        }
    }

    /**
     * Mở khóa license
     */
    public function unlock(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $license = PartnerLicense::forAuthCompany()
                ->where('status', PartnerLicense::STATUS_PENDING)
                ->findOrFail($id);

            // Kiểm tra xem license có còn hạn không
            if ($license->expired_at && $license->expired_at->isPast()) {
                return api_response(false, 'Không thể mở khóa giấy phép đã hết hạn!', null, 422);
            }

            $license->status = PartnerLicense::STATUS_ACTIVATED;
            $license->save();
            DB::commit();
            return api_response(true, 'Mở khóa giấy phép thành công!', $license);
        } catch (Exception $e) {
            DB::rollBack();
            return api_response(false, $e->getMessage(), null, 422);
        }
    }

    /**
     * Gia hạn license (lấy license mới với status=2 để gán cho pms_unit_ref)
     */
    public function renew(Request $request, $id)
    {
        // Validate input
        $request->validate([
            'month' => 'required|integer|in:1,3,6,12,24,36',
            'start_date' => 'required|date|after_or_equal:today'
        ]);

         $db1 = \DB::connection(); // default connection
        $db2 = \DB::connection('mysql_pms'); // replace 'mysql2' with your second connection name
        $db1->beginTransaction();
        $db2->beginTransaction();

        try {
            $expiredLicense = PartnerLicense::forAuthCompany()->findOrFail($id);

            // Kiểm tra xem license có hết hạn không
            if (!$expiredLicense->expired_at || !$expiredLicense->expired_at->isPast()) {
                return api_response(false, 'Chỉ có thể gia hạn giấy phép đã hết hạn!', null, 422);
            }

            // Kiểm tra xem project_unit_ref đã có license đang hoạt động (status = 2) chưa
            if (PartnerLicense::hasActiveLicense($expiredLicense->project_unit_ref)) {
                $activeLicense = PartnerLicense::getActiveLicense($expiredLicense->project_unit_ref);
                return api_response(false, 'Đơn vị này đã có giấy phép đang hoạt động (Mã: ' . $activeLicense->code . '). Không thể gia hạn thêm!', null, 422);
            }

            $selectedMonth = $request->input('month');
            $startDate = \Carbon\Carbon::parse($request->input('start_date'));

            // Tìm license mới có status = ASSIGNED (1) cùng project_code và month được chọn
            $newLicense = PartnerLicense::where('company_id', auth('partner-api')->user()->company_id)
                ->where('status', PartnerLicense::STATUS_ASSIGNED)
                //->where('project_code', $expiredLicense->project_code)
                ->where('month', $selectedMonth)
                ->first();

            if (!$newLicense) {
                return api_response(false, "Không tìm thấy giấy phép {$selectedMonth} tháng để gia hạn!", null, 422);
            }

            // Tính ngày kết thúc = ngày bắt đầu + số tháng - 1 ngày
            $endDate = $startDate->copy()->addMonths($selectedMonth)->subDay();

            // Cập nhật license mới với thông tin từ license cũ
            $newLicense->project_unit_ref = $expiredLicense->project_unit_ref;
            $newLicense->project_account = $expiredLicense->project_account;
            $newLicense->project_code = $expiredLicense->project_code;
            $newLicense->status = PartnerLicense::STATUS_ACTIVATED;
            $newLicense->activated_at = $startDate;
            $newLicense->expired_at = $endDate;
            $newLicense->save();

            // Cập nhật license cũ thành trạng thái hết hạn
            $expiredLicense->status = PartnerLicense::STATUS_EXPIRED;
            $expiredLicense->save();

            if ($newLicense) {
                $unit = (new \Modules\PartnerManagement\Jobs\UpdatePMSAccountJob([
                    'unit_id' => $newLicense->project_unit_ref,
                    'before_username' => $newLicense->project_account,
                    'after_username' => $newLicense->project_account,
                    'activated_at' => $newLicense->activated_at,
                    'expired_at' => $newLicense->expired_at,
                ]))->handle();

                if ($unit == null) throw new \Exception("Không tìm thấy đơn vị");

                // Clear cache sau khi update thành công
                \Cache::forget('partner_license_activities_' . $id);
                \Cache::forget('partner_license_unit_' . $newLicense->project_unit_ref);

                $unit['license'] = $newLicense;
                $db1->commit();
                $db2->commit();
                return api_response(true, "Gia hạn giấy phép thành công với thời hạn {$selectedMonth} tháng!", [
                    'license' => $newLicense,
                    'start_date' => $startDate->format('d/m/Y'),
                    'end_date' => $endDate->format('d/m/Y')
                ]);
            }
        } catch (Exception $e) {
            $db1->rollBack();
            $db2->rollBack();
            return api_response(false, $e->getMessage(), null, 422);
        }
    }

    /**
     * Khóa license (deprecated - sử dụng lock thay thế)
     */
    public function deactivate(Request $request, $id)
    {
        return $this->lock($request, $id);
    }

    /**
     * Cập nhật thông tin license
     */
    public function update(QLMNUpdateRequest $request, $id)
    {
        $db1 = \DB::connection(); // default connection
        $db2 = \DB::connection('mysql_pms'); // replace 'mysql2' with your second connection name
        $db1->beginTransaction();
        $db2->beginTransaction();
        try {
            $current_user = auth('partner-api')->user();
            $data = $request->validated();

            // Kiểm tra license có hết hạn không trước khi cho phép sửa
            $checkLicense = PartnerLicense::forAuthCompany()->findOrFail($data['id']);
            if ($checkLicense->expired_at && $checkLicense->expired_at->isPast()) {
                return api_response(false, 'Không thể chỉnh sửa giấy phép đã hết hạn!', null, 422);
            }

            $license = (new \Modules\PartnerManagement\Jobs\UpdateLicenseCodeForQLMNJob(
                $data['id'],
                $current_user->company_id,
                $data['project_account'],
                $data['activated_at']
            ))->handle();

            if ($license == null) throw new \Exception("Không có giấy phép mã #" . $data['id']);

            if ($license) {
                $unit = (new \Modules\PartnerManagement\Jobs\UpdatePMSAccountJob([
                    'unit_id' => $license['before']['project_unit_ref'],
                    'before_username' => $license['before']['project_account'],
                    'after_username' => $license['after']['project_account'],
                    'activated_at' => $license['after']['activated_at'],
                    'expired_at' => $license['after']['expired_at'],
                ]))->handle();

                if ($unit == null) throw new \Exception("Không tìm thấy đơn vị");

                // Clear cache sau khi update thành công
                \Cache::forget('partner_license_activities_' . $data['id']);
                \Cache::forget('partner_license_unit_' . $license['before']['project_unit_ref']);

                $unit['license'] = $license;
                $db1->commit();
                $db2->commit();
                return api_response(true, 'Cập nhật giấy phép thành công!', $unit);
            }
        } catch (Exception $e) {

            $db1->rollBack();
            $db2->rollBack();
            //dd($e);
            return api_response(false, $e->getMessage(), null, 422);
        }
    }
}
